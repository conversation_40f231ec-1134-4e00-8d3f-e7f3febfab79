﻿
知识点  完全平方公式
1. 推导过程 ———— 依据多项式乘多项式的运算法则
(a+b)2=(a+b)(a+b)=a2+ab+ab+b2=a2+2ab+b2;
(a−b)2=(a−b)(a−b)=a2−ab−ab+b2=a2−2ab+b2。
2. 完全平方公式
符号表示	语言叙述
(a+b)2=a2+2ab+b2,	两数和(或差)的平方, 等于它们的平方和加上(或减去)它们积的2倍。
(a−b)2=a2−2ab+b2。	
注意 运用完全平方公式时, 要弄清楚算式中是两项和还是两项差的平方, 进而判断运算结果中是要加这两项积的2倍还是减这两项积的2倍。
巧学妙记
首平方, 尾平方,
积的2倍在中央,
和是加来差是减,
完全平方要记全。

示例 剖析
利用完全平方公式计算
例1：
(m+2n)2
=m2+2cdotmcdot(2n)+(2n)2
=m2+4mn+4n2
(y−4)2
=y2−2cdotycdot4+42
=y2−8y+16
拓展
三项或三项以上的和(或差)的平方可转化为两项的和(或差)的平方, 如 (a+b+c)2=[(a+b)+c]2=(a+b)2+2cdot(a+b)cdotc+c2=a2+2ab+b2+2ac+2bc+c2=a2+b2+c2+2ab+2ac+2bc; 同理, (a−b−c)2=a2+b2+c2−2ab−2ac+2bc。
3. 完全平方公式的几何解释
图形

阴影面积
(a+b)2 或 a2+2ab+b2
等量关系
各图中阴影部分的面积相等
结论
(a+b)2=a2+2ab+b2
图形

阴影面积
(a−b)2 或 a2−2(a−b)b−b2=a2−2ab+b2
等量关系
各图中阴影部分的面积相等
结论
(a−b)2=a2−2ab+b2
根据数学史家考证对现存古巴比伦时期泥板的分析, 古巴比伦人已经能够运用完全平方公式或估算正方形的对角线长.
用图形解释完全平方公式的方法还有很多, 例举方式如下:
a) (a+b)2=a2+2ab+b2 或 (a+b)2=frac12(a+a+b)bcdot2+b2
b) (a−b)2=a2−2ab+b2
典例
计算 $(-3x - y)^2$ 的结果是（  $\quad$  ）
A. $9x^2 - 6xy + y^2$ 
B. $-9x^2 - 6xy - y^2$ 
C. $9x^2 + 6xy + y^2$ 
D. $9x^2 + y^2$
