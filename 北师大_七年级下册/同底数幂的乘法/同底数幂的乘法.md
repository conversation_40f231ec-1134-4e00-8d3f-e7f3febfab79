﻿知识点  同底数幂的乘法

1. 推导过程
若 m, n 都是正整数, 则
amcdotan=(underbraceacdotacdotsa∗mtext个a)cdot(underbraceacdotacdotsa∗ntext个a)=underbraceacdotacdotsa_(m+n)text个a=am+n。
尝试 思考
你还记得有理数乘方的意义吗?
如 36 的意义，是指 6 个 3 相乘
说明: 幂的底数 a 可以是单项式也可以是多项式
2. 运算性质
文字叙述	符号表示	示例
同底数幂相乘, 底数不变, 指数相加。	指数相加</br>amcdotan=am+n (m, n 都是正整数)。</br>底数不变	指数相加</br>x2cdotx3=x2+3=x5。</br>底数不变
注意 切不可将幂的意义 (几个相同数的乘法) 与乘法的意义 (几个相同数的加法) 混淆, 即 a+a+dots+anea3。

教材延伸
同底数幂乘法运算性质的推广
同底数幂的乘法的运算法则也适用于三个或三个以上的同底数幂相乘, 即 amcdotancdotap=am+n+p (m, n, p 都是正整数), am_1cdotam_2cdotsam_n=am_1+m_2+dots+m_n (m_1,m_2,dots,m_n 都是正整数)。
同底数幂的乘法的运算法则的逆用: am+n=amcdotan (m, n 都是正整数)。

图示
acdotacdota=a3
同底数幂相乘
多项式是同底数幂相乘，咱俩可以加在一起了！

例题点拨
幂的运算中的常用变形：
$(-a)^n = \begin{cases} a^n & (n \text{为偶数}), \ -a^n & (n \text{为奇数}); \end{cases}$
$(a-b)^n = \begin{cases} (b-a)^n & (n \text{为偶数}), \ -(b-a)^n & (n \text{为奇数}) \end{cases}$

典例 
计算:
 (1) $z^{6} \cdot z$ 
(2) $(-7)^{3} \cdot(-7)^{5}$ 
(3) $-c^{2} \cdot(-c)^{5}$ 
(4) $(p+2)^{6} \cdot(p+2)^{4}$ 
(5) $(a+b)^{3} \cdot(-a-b)^{6}$ 
(6) $(3m-n)^{5} \cdot(3m-n) \cdot(3m-n)^{2}$