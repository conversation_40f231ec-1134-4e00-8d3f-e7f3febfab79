﻿
知识点  轴对称
1. 轴对称图形
(1) 轴对称图形的概念
如果一个平面图形沿一条直线折叠后，直线两旁的部分能够互相重合，那么这个图形叫做轴对称图形，这条直线叫做对称轴。
示例图形

[图片] 一个被两条对角线和两条中线分成8个部分的圆形图案，并标注了对称轴。

① 这是一个图形

② 直线l即对称轴，两旁的部分互相重合

该图形有4条对称轴

[图片] 展示了多个轴对称图形的例子，包括：

一片枫叶

一只蝴蝶

两个京剧脸谱

一个由多个圆形构成的复杂对称图案，并配文：法国著名画家 V. 瓦萨雷利创作了名画《委加·派尔》，画中仅仅用了“圆形”图案，就形成了一幅动态的轴对称图形。

注意
(1) 对称轴是一条直线，而不是射线或线段。
(2) 轴对称图形的对称轴可以有一条，也可以有多条。
(2) 轴对称图形中的对应元素
对应元素	文字描述	符号语言	图示
对应点	沿对称轴对折后能够重合的点叫做对应点。	点 A, C 的对应点分别是点 B, D。	[图片] 一个四边形ABCD被其对角线BD和AC以及对称轴l分割。
对应线段	沿对称轴对折后能够重合的线段叫做对应线段。	线段 AC 的对应线段是线段 BD。	
对应角	沿对称轴对折后能够重合的角叫做对应角。	∠A, ∠C, ∠1 的对应角分别是 ∠B, ∠D, ∠2。	

2. 两个图形形成轴对称
如果两个平面图形沿一条直线折叠后能够完全重合，那么称这两个图形成轴对称，这条直线叫作这两个图形的对称轴。
注意 成轴对称的两个图形不仅是全等图形，还要有特殊的位置关系，故全等的两个图形不一定成轴对称，但成轴对称的两个图形一定全等。

3. 轴对称图形和两个图形形成轴对称的区别与联系
	轴对称图形	两个图形形成轴对称
图例	[图片] 一个心形	[图片] 两个左右对称的心形

续表
区别	轴对称图形	两个图形形成轴对称
对象不同	一个图形	两个图形
意义不同	一个形状特殊的图形	两个图形之间的形状、大小与位置关系
对称点的位置不同	对称点在同一个图形上	对称点分别在两个图形上
对称轴的条数不同	有一条、多条或无数条	只有一条
联系	(1) 沿某条直线折叠后直线两旁的部分能够完全重合。<br>(2) 若把成轴对称的两个图形看成一个整体，则它是一个轴对称图形；若把轴对称图形沿对称轴分成两部分，则这两部分关于这条直线成轴对称。	
[漫画]

两只卡通孔雀左右对称站立。

旁白：咱俩关于中间这条直线成轴对称，姿势当然得保持一致啦！

一只孔雀说：你怎么模仿我的姿势呢！

另一只孔雀说：咱俩虽然长得一样，但不成轴对称！
典例
观察下列常见物品或其平面图形的示意图，其中通常不是轴对称图形的是（　　）
<img data-s3-enum="ALIYUN_EDU_KNOWLEDGE_HUB"  data-s3-key="prod/question/2025/07/04/dd4666e9-8571-4321-9bb8-edf408d05eda/image.png" src="https://oss-edu-knowledge-hub.qingyulan.net/prod/question/2025/07/04/dd4666e9-8571-4321-9bb8-edf408d05eda/image.png?x-oss-date=20250819T020919Z&x-oss-expires=3599&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250819%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=e830962f023c8c1464bb611ed166db639bca299b5acbbcf66a75d6ea7deea09e">
<img data-s3-enum="ALIYUN_EDU_KNOWLEDGE_HUB"  data-s3-key="prod/question/2025/07/04/25d1ff13-d31b-4a7b-80a0-b1caa39e8b36/image.png" src="https://oss-edu-knowledge-hub.qingyulan.net/prod/question/2025/07/04/25d1ff13-d31b-4a7b-80a0-b1caa39e8b36/image.png?x-oss-date=20250819T020919Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250819%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=62108681c64ce6ce40caa3beb5fd9ac324b37c01458dc51a3ef9ccff7167f8ca">
<img data-s3-enum="ALIYUN_EDU_KNOWLEDGE_HUB"  data-s3-key="prod/question/2025/07/04/021ff059-7575-43b1-9792-55ef89cd6530/image.png" src="https://oss-edu-knowledge-hub.qingyulan.net/prod/question/2025/07/04/021ff059-7575-43b1-9792-55ef89cd6530/image.png?x-oss-date=20250819T020919Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250819%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=0fd693253112ad13f688673e95e603258f813a1a4cba9ed8028d634c7469295d">
<img data-s3-enum="ALIYUN_EDU_KNOWLEDGE_HUB"  data-s3-key="prod/question/2025/07/04/d31cece6-cfe6-44f5-80f6-d771a508588a/image.png" src="https://oss-edu-knowledge-hub.qingyulan.net/prod/question/2025/07/04/d31cece6-cfe6-44f5-80f6-d771a508588a/image.png?x-oss-date=20250819T020919Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250819%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=cf4b547d90f1a625ce930e01d5e9f2a8df4256113d85bc6784af79eb8219d998">