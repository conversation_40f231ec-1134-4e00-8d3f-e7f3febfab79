﻿

知识点  设计简单的概率模型 
利用几何图形设计概率模型的几种类型
(1) 利用正方形或长方形设计几何概率模型
因为几何概率与几何图形有关，所以在设计时，一般要将几何图形分割成面积相等的图形，可以分割为正方形、长方形或三角形。设计一个概率为 nk​ (k≤n, k, n 为正整数) 的几何概率模型时，需要将整个几何图形均分为n份，其中符合条件A的要有k份。
(2) 利用扇形设计几何概率模型
利用扇形设计一个概率为 nk​ (k≤n, k, n 为正整数) 的几何概率模型时，需要将圆等分成n个扇形，其中符合条件A的要有k个扇形。

图片中有一个插图，包含两个卡通恐龙的对话。

粉色恐龙：你在干什么？

绿色恐龙：我在设计游戏图案，它能使弹珠自由滚动到紫色区域的概率是 83​。


典例
一个圆形转盘被均分为 18 个扇形，已有 1 个扇形涂阴影。若要使指针停在阴影区域的概率为 $\dfrac{1}{3}$，需再涂几个扇形？  
<img data-s3-enum="ALIYUN_EDU_KNOWLEDGE_HUB"  data-s3-key="prod/geogebra/2025/06/23/26cfc2b9-00b8-4940-8e26-756576d557d6/base64-1750693996985.png" src="https://oss-edu-knowledge-hub.qingyulan.net/prod/geogebra/2025/06/23/26cfc2b9-00b8-4940-8e26-756576d557d6/base64-1750693996985.png?x-oss-date=20250818T020720Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250818%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=62f565f662fe97ed84a84a4ec2c9d55e4ddb59a5e72e52375088e6b92b878df9">