﻿
知识点  单项式除以单项式
运算法则
单项式相除，把系数、同底数幂分别相除后，作为商的因式；对于只在被除式里含有的字母，则连同它的指数一起作为商的一个因式。
说明 单项式除以单项式的实质是将其转化为同底数幂的除法运算，且运算结果仍是单项式。
示例
① 系数相除
4a2b÷2a=(4÷2)(a2÷a)b=2ab
② 同底数幂相除
③ 只在被除式里出现的字母及其指数

典例
计算：$ \left(-a^{3n+1} b^{2n}\right)^{2} \div \left[\left(-a^{2} b\right)^{3}\right]^{n} $

思考：
思考： 怎么计算 25a3b2c÷5ab2 呢？（类比分数的约分）
5ab225a3b2c​=5ab25a2c⋅5ab2​=5a2c，或除法
是乘法的逆运算，如 5ab2⋅()=25a3b2c。
回顾同底数幂的除法法则：
am÷an=am−n (a=0, m, n 都是正整数，且m>n）。

对话框

除式里没有我的同伴。

找到你们的同伴后，进行同底数幂的除法运算吧！

那你孤单地作为我的一个因式出现。
