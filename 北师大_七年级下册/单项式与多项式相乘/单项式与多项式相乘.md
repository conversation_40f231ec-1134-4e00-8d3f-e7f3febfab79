﻿
知识点 单项式与多项式相乘 
1. 单项式乘多项式的运算法则
运算法则
单项式与多项式相乘, 就是根据分配律用单项式去乘多项式的每一项, 再把所得的积相加。
符号表示
m(a+b+c)=ma+mb+mc (m, a, b, c 都是单项式)。
说明: 实质上是利用乘法分配律将其转化为多个单项式乘积的和的形式。

高危预警,你把我搞混淆啦!
3a(4m+6n)=3a⋅4m+3a⋅6n

示例
单项式
2a
多项式
a2−ab
2a(a2−ab)=2a⋅a2+2a⋅(−ab)=2a3−2a2b
(箭头说明: 单项式分别乘多项式中的每一项)

2. 用图形解释
运用了数形结合思想
如图1.2-1, 大长方形的面积为 m(a+b+c), 其中3个小长方形的面积分别为 ma,mb,mc, 所以大长方形的面积还可表示为 ma+mb+mc, 所以有 m(a+b+c)=ma+mb+mc。
(图1.2-1: 一个宽为 m、长为 a+b+c 的长方形)

典例
计算：$5b\left(2b^{2} - \dfrac{1}{2}b + 3\right)$

易错: 勿漏乘常数项
注意: 单项式与多项式相乘的结果仍是多项式, 其项数与多项式的项数相同。
易错: 多项式的每一项包括它前面的符号, 不要漏掉“-”号。