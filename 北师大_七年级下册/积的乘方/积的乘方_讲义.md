﻿# 乘积的乘方运算法则
## 一、 课堂引入

想象一下，我们有一个正方体形状的魔术箱，它的边长是 6 厘米，那么它的体积是多少呢？很容易计算，是 $6^3$ 立方厘米。现在，如果我告诉大家，这个魔术箱的边长实际上是由两部分构成的，是 $(2×3)$ 厘米，那么它的体积我们就可以表示为 $(2×3)^3$。这个式子的底数是一个乘积，这就是我们今天要研究的对象。

我们换个思路想一想，这个体积是否也能用别的方式计算呢？当然可以，我们可以先将边长中的两个因数分别立方，得到 $2^3$ 和 $3^3$，再将它们相乘。我们来算一下：$(2×3)^3=6^3=216$，而 $2^3×3^3=8×27=216$。两种方法的结果完全相同！

这仅仅是一个巧合吗？还是背后隐藏着一个普遍的数学规律呢？今天，我们就来一起探索这个被称为"积的乘方"的奥秘。

## 二、 知识讲解

### 1. 积的乘方法则的推导

我们来探究一下，对于任意的底数 $a,b$ 和正整数 $n$，$(ab)^n$ 等于什么。

根据幂的定义（即乘方的意义），一个数（或式子）的 $n$ 次方就等于 $n$ 个这个数（或式子）相乘。所以：

$$(ab)^n = \underbrace{(ab) \cdot (ab) \cdots (ab)}_{n \text{个 } ab}$$

接下来，根据乘法交换律和结合律，我们可以把所有的 $a$ 放在一起相乘，所有的 $b$ 放在一起相乘：

$$= \underbrace{(a \cdot a \cdots a)}_{n \text{个 } a} \cdot \underbrace{(b \cdot b \cdots b)}_{n \text{个 } b}$$

再次根据幂的定义，$n$ 个 $a$ 相乘就是 $a^n$，$n$ 个 $b$ 相乘就是 $b^n$。所以我们得到：

$$= a^n b^n$$

通过这个严谨的推导，我们得到了积的乘方的运算法则。

### 2. 积的乘方法则

**文字叙述：** 积的乘方，等于把积的每一个因式（或因数）分别乘方，再把所得的幂相乘。

**符号表示：**
$$(ab)^n = a^n b^n \quad \text{（其中 } n \text{ 是正整数）}$$

**核心要点：**
在使用这个性质时，关键在于要将括号内"每一个"因式都进行乘方，包括数字系数、字母和代数式，千万不能遗漏。例如，$(2x)^2$ 应计算为 $2^2 \cdot x^2 = 4x^2$，而不是错误的 $2x^2$。

### 3. 法则的延伸与逆用

**法则延伸：**
这个性质同样适用于三个或三个以上的因式的积的乘方。例如：
$$(abc)^n = a^n b^n c^n \quad \text{（其中 } n \text{ 是正整数）}$$

**法则逆用：**
反过来，当几个幂的指数相同时，我们也可以把它们的底数相乘，指数保持不变。即：
$$a^n b^n = (ab)^n \quad \text{（其中 } n \text{ 是正整数）}$$

逆向运用法则有时能让复杂的计算变得异常简单。例如，计算 $4^5 \times (-0.25)^5$，如果直接计算会非常麻烦，但使用逆运算就非常简单：
$$4^5 \times (-0.25)^5 = (4 \times (-0.25))^5 = (-1)^5 = -1$$

## 三、 典例精讲

通过下面的例题，我们来巩固今天所学的知识，并体会它如何与之前学习的幂的运算法则结合使用。

### 例1：
计算：$\left(-\frac{3}{2}p^2q^4\right)^3$

**解题思路：**
首先，要识别出括号内的底数是由三个因式组成的：系数 $-\frac{3}{2}$、字母的幂 $p^2$ 和 $q^4$。然后，根据积的乘方法则，将指数 3 "分配"给这三个因式中的每一个。最后，分别计算每个部分的乘方，其中字母部分需要用到"幂的乘方"法则。

**解：**
$$\begin{aligned}
\left(-\frac{3}{2}p^2q^4\right)^3 &= \left(-\frac{3}{2}\right)^3 \cdot (p^2)^3 \cdot (q^4)^3 \\
&= -\frac{27}{8} \cdot p^{2 \times 3} \cdot q^{4 \times 3} \\
&= -\frac{27}{8}p^6q^{12}
\end{aligned}$$

（第一步：运用积的乘方法则）  
（第二步：计算系数乘方，并运用幂的乘方法则）  
（第三步：得出最终结果）

### 例2：
计算：$(3 \times 10^{-4})^3 \times (5 \times 10^2)^2$

**解题思路：**
这是一个多步运算的题目。第一步，分别对两个括号内的式子使用积的乘方法则。第二步，将计算得到的式子重新组合，把数字系数放在一起，把同底数的幂（10 的幂）放在一起。第三步，分别计算系数的积和同底数幂的积，后者需要用到"同底数幂的乘法"法则。

**解：**
$$\begin{aligned}
&(3 \times 10^{-4})^3 \times (5 \times 10^2)^2 \\
&= (3^3 \times (10^{-4})^3) \times (5^2 \times (10^2)^2) \\
&= (27 \times 10^{-12}) \times (25 \times 10^4) \\
&= (27 \times 25) \times (10^{-12} \times 10^4) \\
&= 675 \times 10^{-12+4} \\
&= 675 \times 10^{-8} \\
&= 6.75 \times 10^{-6}
\end{aligned}$$

（第一步：分别运用积的乘方法则）  
（第二步：计算各部分乘方）  
（第三步：使用乘法交换律和结合律重新组合）  
（第四步：运用同底数幂的乘法法则）  
（第五步：得出结果）  
（可选择：将结果表示为科学记数法）
### 例3：
计算：$(ab^2c^3)^n \cdot (a^2bc)^{2n}$

**解题思路：**
这道题的指数是字母，看起来很抽象，但运算法则完全适用。我们只需将 $n$ 和 $2n$ 看作普通的指数，严格按照运算顺序执行即可。步骤与例2类似：先分别对两个括号使用积的乘方和幂的乘方法则，然后合并同底数幂。

**解：**
$$\begin{aligned}
&(ab^2c^3)^n \cdot (a^2bc)^{2n} \\
&= (a^n \cdot (b^2)^n \cdot (c^3)^n) \cdot ((a^2)^{2n} \cdot b^{2n} \cdot c^{2n}) \\
&= (a^n b^{2n} c^{3n}) \cdot (a^{4n} b^{2n} c^{2n}) \\
&= (a^n \cdot a^{4n}) \cdot (b^{2n} \cdot b^{2n}) \cdot (c^{3n} \cdot c^{2n}) \\
&= a^{n+4n} \cdot b^{2n+2n} \cdot c^{3n+2n} \\
&= a^{5n} b^{4n} c^{5n}
\end{aligned}$$

（第一步：分别运用积的乘方法则）  
（第二步：运用幂的乘方法则）  
（第三步：重新组合）  
（第四步：运用同底数幂的乘法法则）  
（第五步：合并指数，得出最终结果）

## 四、 归纳总结

今天我们学习了积的乘方，它是幂运算三大核心性质的最后一块拼图。现在，我们对本节课的内容进行梳理和总结。

**核心法则：**
$$(ab)^n = a^n b^n$$
积的乘方等于各因式乘方的积。

**法则逆用：**
$$a^n b^n = (ab)^n$$
指数相同时，幂的乘积等于底数乘积的幂。

为了帮助大家更清晰地区分我们已经学过的三个幂运算法则，防止混淆，我们通过下面的表格进行对比：

| 运算类型 | 运算法则 | 公式表示 | 关键特征（判断依据） |
|----------|----------|----------|----------------------|
| 同底数幂的乘法 | 底数不变，指数相加 | $a^m \cdot a^n = a^{m+n}$ | 同底数 的幂进行 乘法 |
| 幂的乘方 | 底数不变，指数相乘 | $(a^m)^n = a^{mn}$ | 单个底数，多重指数 (括号内外) |
| 积的乘方 | 各因数分别乘方，再将幂相乘 | $(ab)^n = a^n b^n$ | 底数为乘积，整体进行 乘方 |

