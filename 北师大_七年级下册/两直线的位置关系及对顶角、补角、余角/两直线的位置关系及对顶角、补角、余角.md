﻿

知识点  两直线的位置关系及对顶角、补角、余角
1. 同一平面内两直线的位置关系
在同一平面内，两条直线的位置关系有相交和平行两种。具体如下表：
	概念	表示方法
相交线	若两条直线只有一个公共点，我们就称这两条直线为相交线。	<br> 直线AB与直线CD相交于点O。
平行线	在同一平面内，不相交的两条直线叫作平行线。	<br> 直线AB与直线CD平行，记作AB∥CD。
注意：平行线是指“两条直线”，而不是两条线段或射线。线段或射线平行是指它们所在的直线平行。
旁注：

“咱俩没有交点呀！”

“是呀，咱俩是平行线。”

1657年，英国数学家奥特雷德在《三角学》一书中首次用“∥”作为表示两条直线平行的符号，并沿用至今。

2. 对顶角的概念及性质
两条直线相交成四个角，其中有相对的两角，故引入对顶角的概念及性质，具体见下表：
	图示	概念	性质	符号语言
对顶角	<br> 直线AB与直线CD相交于点O。	∠1与∠2有公共顶点O，它们的两边互为反向延长线，具有这种位置关系的两个角叫作对顶角。∠3与∠4也是对顶角。	对顶角相等。	因为∠1与∠2是对顶角，所以∠1 = ∠2。 <br> 因为∠3与∠4是对顶角，所以∠3 = ∠4。
补充：图示中∠1与∠3有一条公共边OB，它们的另一边互为反向延长线，具有这种位置关系的两个角互为邻补角。

示例	
对顶角	② 两边互为反向延长线<br>D<br>C<br>① 有公共顶点<br>B<br>说明“对顶角相等”：<br>因为∠AOB和∠COD都是平角，所以∠AOD + ∠1 = ∠AOD + ∠2 = 180°，所以∠1 = ∠2。
注意：
(1) 对顶角是成对出现的，包含两个角之间的位置关系和数量关系，单独的一个角不能称为对顶角。
(2) 若两个角是对顶角，则它们一定相等，但相等的两个角不一定是对顶角。

例题点拨
判断两个角是否互为对顶角的方法

两角是否有公共顶点

两边是否互为反向延长线 (不是对顶角)

是 -> 是 -> 是对顶角
旁注：

“咱俩没有公共顶点，肯定不是对顶角。”

相等的两个角不一定是对顶角。

3. 补角、余角的概念及性质
两条直线相交成四个角，其中相邻的两角的和为180°，故引入补角的概念及性质，类似地补充余角的概念及性质，具体见下表：
	概念	符号表示	性质
补角	如果两个角的和是180°，那么称这两个角互为补角。	若∠1 + ∠2 = 180°，则∠1与∠2互为补角(或互补)。	同角或等角的补角相等。
余角	如果两个角的和是90°，那么称这两个角互为余角。	若∠1 + ∠2 = 90°，则∠1与∠2互为余角(或互余)。	同角或等角的余角相等。
说明：

两角互补 <-> 两角可能都是直角

两角互补 <-> 两角可能一个锐角，一个钝角

两角互余 <-> 两角都是锐角


示例	补角、余角的性质
示例2	<br> 若∠1+∠2=180°, ∠3(∠1)+∠4=180° => ∠2=∠4 (等角的补角相等) <br> 若∠1+∠2=90°, ∠3(∠1)+∠4=90° => ∠2=∠4 (等角的余角相等)
倒题点拨
两个角是否互为补角(或余角)只与它们的和是否等于180°(或90°)有关，即只强调了两角的数量关系，而与它们的位置关系无关。
典例
如图, 直线 $AOB$ 过点 $O$, $OC \perp AB$ 于 $O$. 射线 $OD$ 在 $\angle AOC$ 内部, 射线 $OE$ 在 $\angle BOC$ 内部. 已知 $\angle AOD$ 与 $\angle COE$ 互为余角。
(1) $\angle COD$ 与 $\angle COE$ 有何关系？请说明理由。
(2) $\angle AOD$ 与 $\angle BOE$ 有何关系？请说明理由。
(3) 若 $\angle AOD = 50^{\circ}$, 求 $\angle DOE$ 的度数。  
<img data-s3-enum="ALIYUN_EDU_KNOWLEDGE_HUB"  data-s3-key="prod/geogebra/2025/07/21/5579b78a-191a-4aa7-ba40-5d0f4c7631bb/base64-1753059397108.png" src="https://oss-edu-knowledge-hub.qingyulan.net/prod/geogebra/2025/07/21/5579b78a-191a-4aa7-ba40-5d0f4c7631bb/base64-1753059397108.png?x-oss-date=20250816T060407Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250816%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=06f0377be0fb90c7b407f8f4fd307c1bd0d17e6391853008d7bc9d296655c396">