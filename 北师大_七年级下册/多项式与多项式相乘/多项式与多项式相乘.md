﻿
知识点  多项式与多项式相乘 
1. 多项式乘多项式的运算法则
运算法则	符号表示
多项式与多项式相乘，先用一个多项式的每一项乘另一个多项式的每一项，再把所得的积相加。	(m+a)(n+b)=mn+mb+an+ab <br> (m, n, a, b都是单项式)。

注意 计算多项式乘多项式时，要按照一定的顺序进行，做到不重不漏。初学者可以通过画箭头的方式避免漏乘或多乘。
示例3 多项式乘多项式
(1) (a+1)(a−2)
=acdota+acdot(−2)+1cdota+1times(−2)
=2a2−4a+a−2
            合并同类项
=2a2−3a−2
用一个多项式的每一项去乘另一个多项式的每一项
2. 用图形解释：如图1.2-2所示，把大长方形看成一个整体，则大长方形的面积是 (m+a)(n+b)；把大长方形分成四部分，则大长方形的面积是 mn+mb+an+ab。大长方形的面积 = 分成的四部分的面积的和，即 (m+a)(n+b)=mn+mb+an+ab。
典例
计算：(1) $(5m-3)(2m+6)$ 
(2) $(r+1)(r^2 - r +1)$

---易错：不要漏掉“-”号和不含字母的项
            注意：有同类项的要合并同类项
---运用了化归思想
---说明：合并同类项之前，项数等于相乘的两个多项式的项数之积
旁白：(x+2)(x+6)=x²+6x+12
你把我忘啦！
诶，哪里不对呢？
多项式与多项式相乘时要做到不重不漏