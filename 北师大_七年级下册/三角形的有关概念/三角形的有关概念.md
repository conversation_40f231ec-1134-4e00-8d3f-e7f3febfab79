﻿
知识点  三角形的相关概念
三角形是一个封闭图形，如图，线段 AB, CD, EF 组成的图形均不符合三角形的定义。
在三角形的表示中，顶点字母不分先后顺序
1. 三角形的定义
由不在同一直线上的三条线段首尾顺次相接所组成的图形叫作三角形。“三角形”可以用符号“△”表示。
2. 三角形的基本元素及表示方法：
基本元素	三个顶点	三条边	三个内角
图示		边：组成三角形的线段	内角：三角形相邻两边所组成的角
	顶点：三角形相邻两边的公共端点		
记作：△ABC 或 △ACB 或 △BAC 等			
表示方法	点 A, B, C (必须用大写字母)	表示方法一：AB, BC, AC<br>表示方法二：a, b, c (顶点A的对边BC用a表示, 边AC, 边AB分别用b,c来表示)	∠A, ∠B, ∠C

典例 
在 $\triangle ABC$ 中，点 $D$ 和 $E$ 位于边 $BC$ 上，且 $B, D, E, C$ 按顺序共线。从顶点 $A$ 分别向点 $D$ 和 $E$ 作线段 $AD$ 和 $AE$。问一共可以形成多少个不同的三角形？( $ \quad $)

 A. $3$ $ \quad $  $ \quad $B. $4$ $ \quad $  $ \quad $C. $5$ $ \quad $  $ \quad $D. $6$  
<img data-s3-enum="ALIYUN_EDU_KNOWLEDGE_HUB"  data-s3-key="uat/geogebra/2025/06/22/d616f19a-cc79-4e18-a426-daf4af4f1c0d/base64-1750601562265.png" src="https://oss-edu-knowledge-hub.qingyulan.net/uat/geogebra/2025/06/22/d616f19a-cc79-4e18-a426-daf4af4f1c0d/base64-1750601562265.png?x-oss-date=20250818T021935Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250818%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=d7f8fcbc4819d4d1c64b7310ca098efb215df6476826133d46a222925ffe1621">

解题通法
确定三角形个数的方法
(1) 按图形形成的过程去数;
(2) 从图中的某一条边开始沿着一定方向去数;
(3) 先固定一个顶点, 然后按照一定顺序不断变换另外两个顶点去数。