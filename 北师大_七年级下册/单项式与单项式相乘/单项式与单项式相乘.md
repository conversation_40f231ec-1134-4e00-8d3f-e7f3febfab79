﻿

知识点  单项式与单项式相乘
单项式乘单项式的运算法则： 单项式与单项式相乘，把它们的系数、相同字母的幂分别相乘，其余字母连同它的指数不变，作为积的因式。
一简记为“两相乘，一不变”

示例 1：单项式乘单项式
2a2⋅3ab=(2×3)⋅(a2⋅a)⋅b=6a3b

① 积的系数： 系数相乘

② 相同字母的幂： 同底数幂相乘

③ 单独字母的幂


教材延伸
单项式乘单项式运算法则的推广
对于三个或三个以上的单项式相乘，单项式乘单项式的运算法则同样适用。
如 −a2b⋅3b3⋅2a3c2=(−1×3×2)⋅(a2⋅a3)⋅(b⋅b3)⋅c2=−6a5b4c2

典例 
计算:  $3 y \cdot \left(\dfrac{2}{5} b y\right) \cdot (-1.5 b z) \cdot \left(-4 y^{3} z^{2}\right)$;

注意： 单个字母要连同它的指数一起作为积的因式。
单项式乘单项式中，若有乘方， 混合运算，则应按“先乘方再乘法”的顺序进行。
将复杂的运算转化为简单的幂运算，运用了化归思想。

操作·交流
你能用同底数幂的乘法和交换律、结合律计算 3x2⋅2xy3 吗？
3x2⋅2xy3=3⋅x2⋅2⋅x⋅y3=(3×2)⋅(x2⋅x)⋅(y⋅y3)=6x3y4。
经历以上运算过程你能总结单项式乘单项式的运算法则吗？

（小人对话）

“现在找到你们的同伴一起进行乘法运算吧！”

“那个单项式里没有我的同伴”

“那你就单独作为我的一个因式出现”

2ab⋅5b2=10ab3


例题点拨
单项式乘单项式的运算步骤
确定

系数： 各项系数相乘作为积的系数

同底数幂： 连同它的指数作为积的因式

单独出现的字母： 连同它的指数作为积的因式