import os
import requests
import json
import glob
import re
import hmac
import hashlib
import base64
from datetime import datetime, timezone
from email.utils import formatdate
import uuid
import mimetypes

# --- 1. 正式环境配置区域 (请务必根据您的实际情况修改) ---

# 知识点文件夹所在的根目录 (根据需要修改)
# 示例: "D:/ProdMathKnowledgePoints"
BASE_DIR = "E:/赵娜工作薄/product/学科教育-语文&数学/数学考情分析/初中课件生成/北师大-九下-57个-牛海宁已审核"

# 正式环境API的基础地址
PROD_API_BASE_URL = "https://admin.qingyulan.net"

# 获取OSS上传凭证的API地址 (已更新为正式环境)
API_TOKEN_URL = f"{PROD_API_BASE_URL}/api/smart-study/edu-knowledge-hub/oss/token?ossEnum=ALIYUN_EDU_KNOWLEDGE_HUB"

# !!! 关键步骤：请务必更新为您自己的、正式环境的、有效的 Cookie !!!
REQUEST_HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
    'x-requested-with': 'XMLHttpRequest',
    'origin': PROD_API_BASE_URL,
    'referer': f'{PROD_API_BASE_URL}/education/chinese-reading-mastery-bootcamp/math/knowledge/list',
    'cookie': "lg=admin-server-eb7af449611c646bd0d0ff560c32365d; lg.sig=HzY_kdhyreDKhCfZLjJHVeP5aVYux_Z9ZIXhFNAkuHE" 
}

# --- 2. 核心功能函数 (已适配正式环境) ---

def get_oss_credentials():
    """从后台API获取临时的OSS上传凭证"""
    try:
        response = requests.get(API_TOKEN_URL, headers=REQUEST_HEADERS)
        response.raise_for_status()
        data = response.json()
        if data.get('code') in ['000000', 0, '0', 200, '200']:
            print("   - [成功] 获取OSS临时凭证成功。")
            return data['data']
        else:
            print(f"   - [错误] 获取OSS凭证失败: {data.get('message')}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"   - [严重错误] 请求OSS凭证API失败: {e}")
        return None
    except json.JSONDecodeError:
        print(f"   - [严重错误] 解析OSS凭证响应失败，请检查Cookie是否过期或无效。响应内容: {response.text}")
        return None

def upload_file_to_oss(file_path, credentials):
    """使用获取到的凭证上传单个文件到OSS"""
    if not credentials: return None
    file_name = os.path.basename(file_path)
    print(f"   - 准备上传文件到OSS: {file_name}")
    try:
        access_key_id, access_key_secret, sts_token = credentials['accessKeyId'], credentials['accessKeySecret'], credentials['securityToken']
        bucket, endpoint = credentials['bucket'], credentials['endpoint']
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        unique_id = uuid.uuid4().hex
        object_key = f"knowledge/images/{timestamp}/{unique_id}/{file_name}"
        put_url = f"https://{bucket}.{endpoint.replace('https://', '')}/{object_key}"
        with open(file_path, 'rb') as f:
            file_content = f.read()
        content_type = mimetypes.guess_type(file_name)[0] or 'application/octet-stream'
        date_str = formatdate(timeval=None, localtime=False, usegmt=True)
        string_to_sign = f"PUT\n\n{content_type}\n{date_str}\nx-oss-security-token:{sts_token}\n/{bucket}/{object_key}"
        signature = base64.b64encode(hmac.new(access_key_secret.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha1).digest()).decode('utf-8')
        oss_headers = {'Content-Type': content_type, 'Authorization': f"OSS {access_key_id}:{signature}", 'x-oss-security-token': sts_token, 'Date': date_str}
        response_upload = requests.put(put_url, headers=oss_headers, data=file_content)
        if response_upload.status_code == 200:
            print(f"   - [成功] 文件已上传到OSS, Key: {object_key}")
            return object_key
        else:
            print(f"   - [错误] OSS上传失败: 状态码 {response_upload.status_code}, 响应: {response_upload.text}")
            return None
    except Exception as e:
        print(f"   - [严重错误] 文件上传过程中发生异常: {e}")
        return None

def register_image_attachments(knowledge_id, attachments_payload):
    """将一个包含多张图片信息的数组(列表)，一次性提交到后台进行关联。"""
    api_url = f"{PROD_API_BASE_URL}/api/edu-knowledge-manage/api/math/knowledge-points/files/upload/{knowledge_id}"
    payload = attachments_payload
    print(f"   - 调用后台API，将 {len(payload)} 张图片信息打包成数组一次性注册...")
    try:
        response = requests.post(api_url, headers=REQUEST_HEADERS, json=payload)
        response_json = response.json()
        if response.status_code == 200 and response_json.get('code') in ['000000', 0, '0', 200, '200']:
            print("   - [成功] 图片附件数组注册成功！")
            return True
        else:
            print(f"   - [失败] 附件注册失败: Code: {response_json.get('code')}, Message: {response_json.get('message')}")
            return False
    except Exception as e:
        print(f"   - [失败] 附件注册请求异常: {e}")
        return False

def update_text_content(knowledge_id, field_name, content):
    """独立的函数，用于更新主内容(content)或讲义内容(handout)"""
    api_url = f"{PROD_API_BASE_URL}/api/edu-knowledge-manage/api/math/knowledge-points/{knowledge_id}"
    payload = {field_name: content}
    print(f"   - 准备独立更新字段 '{field_name}'...")
    try:
        response = requests.put(api_url, headers=REQUEST_HEADERS, json=payload)
        response_json = response.json()
        if response.status_code == 200 and response_json.get('code') in ['000000', 0, '0', 200, '200']:
            print(f"   - [成功] 字段 '{field_name}' 更新成功！")
            return True
        else:
            print(f"   - [失败] 更新失败: Code: {response_json.get('code')}, Message: {response_json.get('message')}")
            return False
    except Exception as e:
        print(f"   - [失败] 更新请求异常: {e}")
        return False

def query_knowledge_point_details(knowledge_id, cookie):
    """查询并打印知识点详情，用于调试验证"""
    print(f"   - [调试] 正在查询知识点 {knowledge_id} 的最新数据...")
    url = f"{PROD_API_BASE_URL}/api/edu-knowledge-manage/api/math/knowledge-points/detail/{knowledge_id}"
    debug_headers = REQUEST_HEADERS.copy()
    debug_headers['cookie'] = cookie
    debug_headers['referer'] = f'{PROD_API_BASE_URL}/education/chinese-reading-mastery-bootcamp/math/knowledge/detail/{knowledge_id}'
    
    try:
        response = requests.get(url, headers=debug_headers, timeout=20)
        if response.ok:
            print("   - [调试] 查询成功，当前服务器数据:")
            # --- FIX STARTS HERE ---
            # Handle cases where 'data' or 'originalImageList' might be null in the API response
            json_response = response.json()
            data = json_response.get('data') or {}
            attachments = data.get('originalImageList') or []
            # --- FIX ENDS HERE ---

            print(f"     - 图片附件数量: {len(attachments)}")
            if attachments:
                for att in attachments:
                    print(f"       - Key: {att.get('ossKey')}, Sort: {att.get('sortNo')}")
        else:
            print(f"   - [调试] 查询失败。状态码: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   - [调试] 查询时发生网络错误: {e}")


# --- 3. 主程序入口 (逻辑不变) ---
def main():
    """主执行函数"""
    if '在此处粘贴您的正式环境Cookie' in REQUEST_HEADERS['cookie']:
        print("[严重错误] 请在脚本的'配置区域'更新您的正式环境Cookie！")
        return
        
    mapping_file_path = os.path.join(BASE_DIR, 'mapping.json')
    if not os.path.exists(mapping_file_path):
        print(f"[错误] 未在根目录 '{BASE_DIR}' 中找到映射文件 'mapping.json'。")
        return

    try:
        with open(mapping_file_path, 'r', encoding='utf-8') as f:
            knowledge_mapping = json.load(f)
    except Exception as e:
        print(f"[错误] 读取或解析 'mapping.json' 文件失败: {e}")
        return

    total_tasks = len(knowledge_mapping)
    print(f"--- 发现 {total_tasks} 个知识点，开始执行批量上传至【正式环境】---")

    for i, (folder_name, knowledge_id) in enumerate(knowledge_mapping.items()):
        print(f"\n[{i+1}/{total_tasks}] ===> 开始处理知识点: {folder_name} (ID: {knowledge_id}) <===")
        folder_path = os.path.join(BASE_DIR, folder_name)
        if not os.path.isdir(folder_path):
            print(f"   - [警告] 找不到对应的文件夹: {folder_name}，已跳过。")
            continue
        
        # 步骤1: 核心图片处理流程
        print("-> 阶段1: 处理图片附件")
        pattern_multiple = os.path.join(folder_path, f"{folder_name}_*.png")
        pattern_single = os.path.join(folder_path, f"{folder_name}.png")
        image_files = sorted(glob.glob(pattern_multiple) + glob.glob(pattern_single))

        if not image_files:
            print("   - 未发现需要上传的图片文件。")
        else:
            credentials = get_oss_credentials()
            if credentials:
                attachments_payload = [] 
                print(f"   - 发现 {len(image_files)} 张图片，开始上传到OSS并收集信息...")
                for img_path in image_files:
                    match = re.search(r'_(\d+)\.png$', os.path.basename(img_path), re.IGNORECASE)
                    sort_no = int(match.group(1)) if match else 1
                    oss_key = upload_file_to_oss(img_path, credentials)
                    if oss_key:
                        attachments_payload.append({
                            "ossKey": oss_key,
                            "ossEnum": "ALIYUN_EDU_KNOWLEDGE_HUB",
                            "sortNo": sort_no
                        })
                
                if attachments_payload:
                    register_image_attachments(knowledge_id, attachments_payload)
                else:
                    print("   - [警告] 虽然找到了图片文件，但没有一个成功上传到OSS，无法注册。")

        # 步骤2: 独立更新主MD内容
        print("-> 阶段2: 处理主内容MD")
        md_file = os.path.join(folder_path, f"{folder_name}.md")
        if os.path.exists(md_file):
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            update_text_content(knowledge_id, "content", content)
        else:
            print(f"   - 未发现主内容文件: {os.path.basename(md_file)}")

        # 步骤3: 独立更新讲义MD内容
        print("-> 阶段3: 处理讲义内容MD")
        lecture_md_file = os.path.join(folder_path, f"{folder_name}_讲义.md")
        if os.path.exists(lecture_md_file):
            with open(lecture_md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            update_text_content(knowledge_id, "handout", content)
        else:
            print(f"   - 未发现讲义文件: {os.path.basename(lecture_md_file)}")
        
        # 步骤4: 调试验证
        query_knowledge_point_details(knowledge_id, REQUEST_HEADERS['cookie']) 

        print(f"--- 知识点 '{folder_name}' 处理完毕 ---")

    print("\n=========================")
    print("--- 所有任务处理完毕 ---")

if __name__ == "__main__":
    main()